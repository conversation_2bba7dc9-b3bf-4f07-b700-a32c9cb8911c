import t from"./xxhash.wasm";async function e(){return function(t){const{exports:{mem:e,xxh32:n,xxh64:r,init32:i,update32:o,digest32:a,init64:s,update64:u,digest64:h}}=t;let c=new Uint8Array(e.buffer);function f(t,n){if(e.buffer.byteLength<t+n){const r=Math.ceil((t+n-e.buffer.byteLength)/65536);e.grow(r),c=new Uint8Array(e.buffer)}}function g(t,e,n,r,i,o){f(t);const a=new Uint8Array(t);return c.set(a),n(0,e),a.set(c.subarray(0,t)),{update(e){let n;return c.set(a),"string"==typeof e?(f(3*e.length,t),n=w.encodeInto(e,c.subarray(t)).written):(f(e.byteLength,t),c.set(e,t),n=e.byteLength),r(0,t,n),a.set(c.subarray(0,t)),this},digest:()=>(c.set(a),o(i(0)))}}function b(t){return t>>>0}const y=2n**64n-1n;function d(t){return t&y}const w=new TextEncoder,x=0,p=0n;function l(t,e=x){return f(3*t.length,0),b(n(0,w.encodeInto(t,c).written,e))}function L(t,e=p){return f(3*t.length,0),d(r(0,w.encodeInto(t,c).written,e))}return{h32:l,h32ToString:(t,e=x)=>l(t,e).toString(16).padStart(8,"0"),h32Raw:(t,e=x)=>(f(t.byteLength,0),c.set(t),b(n(0,t.byteLength,e))),create32:(t=x)=>g(48,t,i,o,a,b),h64:L,h64ToString:(t,e=p)=>L(t,e).toString(16).padStart(16,"0"),h64Raw:(t,e=p)=>(f(t.byteLength,0),c.set(t),d(r(0,t.byteLength,e))),create64:(t=p)=>g(88,t,s,u,h,d)}}(await WebAssembly.instantiate(t))}export{e as default};
//# sourceMappingURL=xxhash-wasm.js.map
