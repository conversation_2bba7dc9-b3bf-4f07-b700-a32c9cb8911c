using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

var builder = WebApplication.CreateBuilder(args);

// 🔹 Controller desteği ekleniyor
builder.Services.AddControllers();

// 🔹 Swagger/OpenAPI desteği ekleniyor
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "MCP SQL API", Version = "v1" });
});

// 🔹 CORS desteği ekleniyor
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// 🔹 Development ortamında Swagger UI'ı etkinleştir
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "MCP SQL API v1");
        c.RoutePrefix = string.Empty; // Swagger UI'ı root'ta göster
    });
}

// 🔹 CORS middleware'ini etkinleştir
app.UseCors("AllowAll");

// 🔹 HTTPS yönlendirme (zorunlu değil ama önerilir)
app.UseHttpsRedirection();

// 🔹 Controller route'ları tanımlanıyor
app.MapControllers();

app.Run();
