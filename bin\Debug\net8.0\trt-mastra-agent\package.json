{"name": "trt-mastra-agent", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "start": "mastra start", "server": "tsx src/server.ts", "server:dev": "tsx watch src/server.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/google": "^1.2.21", "@mastra/core": "^0.10.9", "@mastra/libsql": "^0.11.0", "@mastra/loggers": "^0.10.2", "@mastra/memory": "^0.11.1", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "zod": "^3.25.67"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.10", "mastra": "^0.10.9", "tsx": "^4.20.3", "typescript": "^5.8.3"}}