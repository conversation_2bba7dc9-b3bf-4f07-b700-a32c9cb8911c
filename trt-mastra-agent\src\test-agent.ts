import { sqlAgent } from './mastra/agents/sqlAgent';
import dotenv from 'dotenv';

// Environment variables'ları yükle
dotenv.config();

async function testAgent() {
    console.log('🧪 SQL Agent Test Başlıyor...\n');

    const testQueries = [
        "Ahmet'in maaş bilgisini getir",
        "Yaşı 30'dan küçük erkekleri getir", 
        "Maaşı 3000'den büyük personelleri getir",
        "Kadın personelleri getir",
        "En yüksek maaşlı personeli getir"
    ];

    for (const query of testQueries) {
        console.log(`\n📝 Test Sorgusu: "${query}"`);
        console.log('─'.repeat(50));
        
        try {
            const result = await sqlAgent.generate(query);
            
            console.log('🤖 Agent Yanıtı:');
            console.log('Text:', result.text);
            
            if (result.toolResults && result.toolResults.length > 0) {
                console.log('\n🔧 Tool Sonuçları:');
                result.toolResults.forEach((toolResult, index) => {
                    console.log(`Tool ${index + 1}:`, JSON.stringify(toolResult, null, 2));
                });
            }
            
            console.log('\n✅ Test başarılı!');
            
        } catch (error) {
            console.error('❌ Test hatası:', error);
        }
        
        // Testler arası bekleme
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('\n🎉 Tüm testler tamamlandı!');
}

// Test'i çalıştır
testAgent().catch(console.error);
