import { Mastra } from '@mastra/core';
import { sqlAgent } from './agents/sqlAgent';
import { sqlWorkflow } from "./workflows/sqlWorkflow";
import dotenv from "dotenv";

// Environment variables'ları yükle
dotenv.config();

// Mastra instance'ını oluştur ve export et
export const mastra = new Mastra({
  agents: {
    sqlAgent: sqlAgent
  }
});

async function main() {
  console.log("🚀 SQL Agent başlatılıyor...");

  // Test sorgusu
  const testQuery = "Maaşı 5000'den büyük ve yaşı 30'dan küçük erkekleri getir";

  try {
    const result = await sqlWorkflow.execute({
      query: testQuery,
    });

    console.log("✅ Sorgu sonucu:", JSON.stringify(result, null, 2));
  } catch (error) {
    console.error("❌ Hata:", error);
  }
}

// Eğer bu dosya doğrudan çalıştırılıyorsa main fonksiyon<PERSON>u çalıştır
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
