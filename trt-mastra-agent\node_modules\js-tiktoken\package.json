{"name": "js-tik<PERSON>en", "version": "1.0.20", "description": "JavaScript port of tiktoken", "license": "MIT", "scripts": {"build": "rm -rf dist && tsup && cp -R src/ranks dist/ranks", "test": "vitest run"}, "type": "module", "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "files": ["dist/**/*", "!dist/ranks/*.tiktoken", "!dist/ranks/*.json", "!dist/ranks/ranks.ts", "index.js", "index.d.ts", "lite.js", "lite.d.ts"], "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./lite": {"types": "./dist/lite.d.ts", "require": "./dist/lite.cjs", "default": "./dist/lite.js"}, "./ranks/cl100k_base": {"types": "./dist/ranks/cl100k_base.d.ts", "require": "./dist/ranks/cl100k_base.cjs", "default": "./dist/ranks/cl100k_base.js"}, "./ranks/gpt2": {"types": "./dist/ranks/gpt2.d.ts", "require": "./dist/ranks/gpt2.cjs", "default": "./dist/ranks/gpt2.js"}, "./ranks/p50k_base": {"types": "./dist/ranks/p50k_base.d.ts", "require": "./dist/ranks/p50k_base.cjs", "default": "./dist/ranks/p50k_base.js"}, "./ranks/p50k_edit": {"types": "./dist/ranks/p50k_edit.d.ts", "require": "./dist/ranks/p50k_edit.cjs", "default": "./dist/ranks/p50k_edit.js"}, "./ranks/r50k_base": {"types": "./dist/ranks/r50k_base.d.ts", "require": "./dist/ranks/r50k_base.cjs", "default": "./dist/ranks/r50k_base.js"}, "./ranks/o200k_base": {"types": "./dist/ranks/o200k_base.d.ts", "require": "./dist/ranks/o200k_base.cjs", "default": "./dist/ranks/o200k_base.js"}}, "repository": {"type": "git", "url": "https://github.com/dqbd/tiktoken"}, "dependencies": {"base64-js": "^1.5.1"}, "publishConfig": {"access": "public"}, "devDependencies": {"tsup": "^6.7.0"}, "tsup": {"entry": ["src/index.ts", "src/lite.ts", "src/ranks/cl100k_base.ts", "src/ranks/gpt2.ts", "src/ranks/p50k_base.ts", "src/ranks/p50k_edit.ts", "src/ranks/r50k_base.ts", "src/ranks/o200k_base.ts"], "format": ["cjs", "esm"], "dts": true, "clean": true, "treeshake": true}}