import { ToolClient } from "mastra";
import fetch from "node-fetch";

export const sqlExecutor: ToolClient = {
  name: "sqlExecutor",
  description: "MCP API üzerinden SQL sorgusu çalıştırır.",
  async run(input: { sql: string }) {
    const response = await fetch("http://localhost:5200/query", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ sql: input.sql }),
    });

    if (!response.ok) {
      throw new Error(`MCP API hata: ${response.statusText}`);
    }

    return await response.json();
  },
};
