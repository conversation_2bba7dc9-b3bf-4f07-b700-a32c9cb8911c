"use strict";var K=Object.defineProperty;var o=(s,e)=>K(s,"name",{value:e,configurable:!0});var Y=require("./get-pipe-path-BoR10qr8.cjs"),u=require("node:module"),m=require("node:path"),L=require("node:url"),b=require("get-tsconfig"),O=require("node:fs"),w=require("./index-gckBtVBf.cjs"),R=require("./client-D6NvIMSC.cjs"),V=require("node:util"),g=require("./index-BWFBUo6r.cjs");const W=o(s=>{if(!s.startsWith("data:text/javascript,"))return;const e=s.indexOf("?");if(e===-1)return;const n=new URLSearchParams(s.slice(e+1)).get("filePath");if(n)return n},"getOriginalFilePath"),D=o(s=>{const e=W(s);return e&&(u._cache[e]=u._cache[s],delete u._cache[s],s=e),s},"interopCjsExports"),Z=o(s=>{const e=s.indexOf(":");if(e!==-1)return s.slice(0,e)},"getScheme"),N=o(s=>s[0]==="."&&(s[1]==="/"||s[1]==="."||s[2]==="/"),"isRelativePath"),j=o(s=>N(s)||m.isAbsolute(s),"isFilePath"),q=o(s=>{if(j(s))return!0;const e=Z(s);return e&&e!=="node"},"requestAcceptsQuery"),v="file://",ee=[".ts",".tsx",".jsx",".mts",".cts"],C=/\.([cm]?ts|[tj]sx)($|\?)/,se=/[/\\].+\.(?:cts|cjs)(?:$|\?)/,te=/\.json($|\?)/,_=/\/(?:$|\?)/,ne=/^(?:@[^/]+\/)?[^/\\]+$/,J=`${m.sep}node_modules${m.sep}`;exports.fileMatcher=void 0,exports.tsconfigPathsMatcher=void 0,exports.allowJs=!1;const Q=o(s=>{let e=null;if(s){const r=m.resolve(s);e={path:r,config:b.parseTsconfig(r)}}else{try{e=b.getTsconfig()}catch{}if(!e)return}exports.fileMatcher=b.createFilesMatcher(e),exports.tsconfigPathsMatcher=b.createPathsMatcher(e),exports.allowJs=e?.config.compilerOptions?.allowJs??!1},"loadTsconfig"),T=o(s=>Array.from(s).length>0?`?${s.toString()}`:"","urlSearchParamsStringify"),re=`
//# sourceMappingURL=data:application/json;base64,`,A=o(()=>process.sourceMapsEnabled??!0,"shouldApplySourceMap"),$=o(({code:s,map:e})=>s+re+Buffer.from(JSON.stringify(e),"utf8").toString("base64"),"inlineSourceMap"),M=Number(process.env.TSX_DEBUG);M&&(g.options.enabled=!0,g.options.supportLevel=3);const I=o(s=>(e,...r)=>{if(!M||e>M)return;const n=`${g.bgGray(` tsx P${process.pid} `)} ${s}`,t=r.map(a=>typeof a=="string"?a:V.inspect(a,{colors:!0})).join(" ");O.writeSync(1,`${n} ${t}
`)},"createLog"),x=I(g.bgLightYellow(g.black(" CJS "))),ae=I(g.bgBlue(" ESM ")),oe=[".cts",".mts",".ts",".tsx",".jsx"],ie=[".js",".cjs",".mjs"],k=[".ts",".tsx",".jsx"],F=o((s,e,r,n)=>{const t=Object.getOwnPropertyDescriptor(s,e);t?.set?s[e]=r:(!t||t.configurable)&&Object.defineProperty(s,e,{value:r,enumerable:t?.enumerable||n?.enumerable,writable:n?.writable??(t?t.writable:!0),configurable:n?.configurable??(t?t.configurable:!0)})},"safeSet"),ce=o((s,e,r)=>{const n=e[".js"],t=o((a,i)=>{if(s.enabled===!1)return n(a,i);const[c,f]=i.split("?");if((new URLSearchParams(f).get("namespace")??void 0)!==r)return n(a,i);x(2,"load",{filePath:i}),a.id.startsWith("data:text/javascript,")&&(a.path=m.dirname(c)),R.parent?.send&&R.parent.send({type:"dependency",path:c});const p=oe.some(h=>c.endsWith(h)),P=ie.some(h=>c.endsWith(h));if(!p&&!P)return n(a,c);let d=O.readFileSync(c,"utf8");if(c.endsWith(".cjs")){const h=w.transformDynamicImport(i,d);h&&(d=A()?$(h):h.code)}else if(p||w.isESM(d)){const h=w.transformSync(d,i,{tsconfigRaw:exports.fileMatcher?.(c)});d=A()?$(h):h.code}x(1,"loaded",{filePath:c}),a._compile(d,c)},"transformer");F(e,".js",t);for(const a of k)F(e,a,t,{enumerable:!r,writable:!0,configurable:!0});return F(e,".mjs",t,{writable:!0,configurable:!0}),()=>{e[".js"]===t&&(e[".js"]=n);for(const a of[...k,".mjs"])e[a]===t&&delete e[a]}},"createExtensions"),le=o(s=>e=>{if((e==="."||e===".."||e.endsWith("/.."))&&(e+="/"),_.test(e)){let r=m.join(e,"index.js");e.startsWith("./")&&(r=`./${r}`);try{return s(r)}catch{}}try{return s(e)}catch(r){const n=r;if(n.code==="MODULE_NOT_FOUND")try{return s(`${e}${m.sep}index.js`)}catch{}throw n}},"createImplicitResolver"),B=[".js",".json"],G=[".ts",".tsx",".jsx"],fe=[...G,...B],he=[...B,...G],y=Object.create(null);y[".js"]=[".ts",".tsx",".js",".jsx"],y[".jsx"]=[".tsx",".ts",".jsx",".js"],y[".cjs"]=[".cts"],y[".mjs"]=[".mts"];const X=o(s=>{const e=s.split("?"),r=e[1]?`?${e[1]}`:"",[n]=e,t=m.extname(n),a=[],i=y[t];if(i){const f=n.slice(0,-t.length);a.push(...i.map(l=>f+l+r))}const c=!(s.startsWith(v)||j(n))||n.includes(J)||n.includes("/node_modules/")?he:fe;return a.push(...c.map(f=>n+f+r)),a},"mapTsExtensions"),S=o((s,e,r)=>{if(x(3,"resolveTsFilename",{request:e,isDirectory:_.test(e),isTsParent:r,allowJs:exports.allowJs}),_.test(e)||!r&&!exports.allowJs)return;const n=X(e);if(n)for(const t of n)try{return s(t)}catch(a){const{code:i}=a;if(i!=="MODULE_NOT_FOUND"&&i!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw a}},"resolveTsFilename"),me=o((s,e)=>r=>{if(x(3,"resolveTsFilename",{request:r,isTsParent:e,isFilePath:j(r)}),j(r)){const n=S(s,r,e);if(n)return n}try{return s(r)}catch(n){const t=n;if(t.code==="MODULE_NOT_FOUND"){if(t.path){const i=t.message.match(/^Cannot find module '([^']+)'$/);if(i){const f=i[1],l=S(s,f,e);if(l)return l}const c=t.message.match(/^Cannot find module '([^']+)'. Please verify that the package.json has a valid "main" entry$/);if(c){const f=c[1],l=S(s,f,e);if(l)return l}}const a=S(s,r,e);if(a)return a}throw t}},"createTsExtensionResolver"),z="at cjsPreparseModuleExports (node:internal",de=o(s=>{const e=s.stack.split(`
`).slice(1);return e[1].includes(z)||e[2].includes(z)},"isFromCjsLexer"),ue=o((s,e)=>{const r=s.split("?"),n=new URLSearchParams(r[1]);if(e?.filename){const t=W(e.filename);let a;if(t){const f=t.split("?"),l=f[0];a=f[1],e.filename=l,e.path=m.dirname(l),e.paths=u._nodeModulePaths(e.path),u._cache[l]=e}a||(a=e.filename.split("?")[1]);const c=new URLSearchParams(a).get("namespace");c&&n.append("namespace",c)}return[r[0],n,(t,a)=>(m.isAbsolute(t)&&!t.endsWith(".json")&&!t.endsWith(".node")&&!(a===0&&de(new Error))&&(t+=T(n)),t)]},"preserveQuery"),pe=o((s,e,r)=>{if(s.startsWith(v)&&(s=L.fileURLToPath(s)),exports.tsconfigPathsMatcher&&!j(s)&&!e?.filename?.includes(J)){const n=exports.tsconfigPathsMatcher(s);for(const t of n)try{return r(t)}catch{}}return r(s)},"resolveTsPaths"),Pe=o((s,e,r)=>(n,t,...a)=>{if(s.enabled===!1)return e(n,t,...a);n=D(n);const[i,c,f]=ue(n,t);if((c.get("namespace")??void 0)!==r)return e(n,t,...a);x(2,"resolve",{request:n,parent:t?.filename??t,restOfArgs:a});let l=o(P=>e(P,t,...a),"nextResolveSimple");l=me(l,!!(r||t?.filename&&C.test(t.filename))),l=le(l);const p=f(pe(i,t,l),a.length);return x(1,"resolved",{request:n,parent:t?.filename??t,resolved:p}),p},"createResolveFilename"),H=o((s,e)=>{if(!e)throw new Error("The current file path (__filename or import.meta.url) must be provided in the second argument of tsx.require()");return s.startsWith(".")?((typeof e=="string"&&e.startsWith(v)||e instanceof URL)&&(e=L.fileURLToPath(e)),m.resolve(m.dirname(e),s)):s},"resolveContext"),ge=o(s=>{const{sourceMapsEnabled:e}=process,r={enabled:!0};Q(process.env.TSX_TSCONFIG_PATH),process.setSourceMapsEnabled(!0);const n=u._resolveFilename,t=Pe(r,n,s?.namespace);u._resolveFilename=t;const a=ce(r,u._extensions,s?.namespace),i=o(()=>{e===!1&&process.setSourceMapsEnabled(!1),r.enabled=!1,u._resolveFilename===t&&(u._resolveFilename=n),a()},"unregister");if(s?.namespace){const c=o((l,p)=>{const P=H(l,p),[d,h]=P.split("?"),E=new URLSearchParams(h);return s.namespace&&!d.startsWith("node:")&&E.set("namespace",s.namespace),Y.require(d+T(E))},"scopedRequire");i.require=c;const f=o((l,p,P)=>{const d=H(l,p),[h,E]=d.split("?"),U=new URLSearchParams(E);return s.namespace&&!h.startsWith("node:")&&U.set("namespace",s.namespace),t(h+T(U),module,!1,P)},"scopedResolve");i.resolve=f,i.unregister=i}return i},"register");exports.cjsExtensionPattern=se,exports.debugEnabled=M,exports.fileUrlPrefix=v,exports.inlineSourceMap=$,exports.interopCjsExports=D,exports.isBarePackageNamePattern=ne,exports.isDirectoryPattern=_,exports.isJsonPattern=te,exports.isRelativePath=N,exports.loadTsconfig=Q,exports.logEsm=ae,exports.mapTsExtensions=X,exports.register=ge,exports.requestAcceptsQuery=q,exports.tsExtensions=ee,exports.tsExtensionsPattern=C;
