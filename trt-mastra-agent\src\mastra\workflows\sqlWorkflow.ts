import { sqlAgent } from "../agents/sqlAgent";

export const sqlWorkflow = {
  name: "sqlWorkflow",
  description: "SQL Agent ve Executor araçlarını bağlar.",

  async execute(input: { query: string }) {
    console.log(`📝 Kullanıcı sorgusu: ${input.query}`);

    try {
      // Agent'ı çalıştır
      const result = await sqlAgent.generate(input.query);

      console.log(`🤖 Agent yanıtı:`, result);

      // Eğer agent tool kullandıysa, tool sonuçlarını al
      if (result.toolResults && result.toolResults.length > 0) {
        const toolResult = result.toolResults[0];
        console.log(`🔧 Tool sonucu:`, toolResult);

        return {
          success: true,
          query: input.query,
          result: {
            text: result.text,
            data: toolResult.result?.data || toolResult.result,
            count: toolResult.result?.count || 0,
            sqlQuery: "Agent tarafından oluşturuldu"
          }
        };
      } else {
        // <PERSON><PERSON>e metin yanıt varsa
        return {
          success: true,
          query: input.query,
          result: {
            text: result.text,
            data: null,
            count: 0
          }
        };
      }
    } catch (error) {
      console.error(`❌ Workflow hatası:`, error);
      throw error;
    }
  }
};
