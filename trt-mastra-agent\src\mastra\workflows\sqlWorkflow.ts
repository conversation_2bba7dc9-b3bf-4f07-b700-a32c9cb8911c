import { sqlAgent } from "../agents/sqlAgent";

export const sqlWorkflow = {
  name: "sqlWorkflow",
  description: "SQL Agent ve Executor araçlarını bağlar.",

  async execute(input: { query: string }) {
    console.log(`Kullanıcı sorgusu: ${input.query}`);

    // Agent'ı çalıştır
    const result = await sqlAgent.generate(input.query);

    return {
      success: true,
      query: input.query,
      result: result
    };
  }
};
