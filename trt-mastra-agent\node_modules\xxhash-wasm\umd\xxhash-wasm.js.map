{"version": 3, "file": "xxhash-wasm.js", "sources": ["../src/index.js", "../src/xxhash.js"], "sourcesContent": ["import { xxhash } from \"./xxhash\";\n\n// The .wasm is filled in by the build process, so the user doesn't need to load\n// xxhash.wasm by themselves because it's part of the bundle. Otherwise it\n// couldn't be distributed easily as the user would need to host xxhash.wasm\n// and then fetch it, to be able to use it.\n// eslint-disable-next-line no-undef\nconst wasmBytes = new Uint8Array(WASM_PRECOMPILED_BYTES);\n\nexport default async function () {\n  return xxhash((await WebAssembly.instantiate(wasmBytes)).instance);\n}\n", "const u32_BYTES = 4;\nconst u64_BYTES = 8;\n\n// The xxh32 hash state struct:\nconst XXH32_STATE_SIZE_BYTES =\n  u32_BYTES + // total_len\n  u32_BYTES + // large_len\n  u32_BYTES * 4 + // Accumulator lanes\n  u32_BYTES * 4 + // Internal buffer\n  u32_BYTES + // memsize\n  u32_BYTES; // reserved\n\n// The xxh64 hash state struct:\nconst XXH64_STATE_SIZE_BYTES =\n  u64_BYTES + // total_len\n  u64_BYTES * 4 + // Accumulator lanes\n  u64_BYTES * 4 + // Internal buffer\n  u32_BYTES + // memsize\n  u32_BYTES + // reserved32\n  u64_BYTES; // reserved64\n\nexport function xxhash(instance) {\n  const {\n    exports: {\n      mem,\n      xxh32,\n      xxh64,\n      init32,\n      update32,\n      digest32,\n      init64,\n      update64,\n      digest64,\n    },\n  } = instance;\n\n  let memory = new Uint8Array(mem.buffer);\n  // Grow the wasm linear memory to accommodate length + offset bytes\n  function growMemory(length, offset) {\n    if (mem.buffer.byteLength < length + offset) {\n      const extraPages = Math.ceil(\n        // Wasm pages are spec'd to 64K\n        (length + offset - mem.buffer.byteLength) / (64 * 1024),\n      );\n      mem.grow(extraPages);\n      // After growing, the original memory's ArrayBuffer is detached, so we'll\n      // need to replace our view over it with a new one over the new backing\n      // ArrayBuffer.\n      memory = new Uint8Array(mem.buffer);\n    }\n  }\n\n  // The h32 and h64 streaming hash APIs are identical, so we can implement\n  // them both by way of a templated call to this generalized function.\n  function create(size, seed, init, update, digest, finalize) {\n    // Ensure that we've actually got enough space in the wasm memory to store\n    // the state blob for this hasher.\n    growMemory(size);\n\n    // We'll hold our hashing state in this closure.\n    const state = new Uint8Array(size);\n    memory.set(state);\n    init(0, seed);\n\n    // Each time we interact with wasm, it may have mutated our state so we'll\n    // need to read it back into our closed copy.\n    state.set(memory.subarray(0, size));\n\n    return {\n      update(input) {\n        memory.set(state);\n        let length;\n        if (typeof input === \"string\") {\n          growMemory(input.length * 3, size);\n          length = encoder.encodeInto(input, memory.subarray(size)).written;\n        } else {\n          // The only other valid input type is a Uint8Array\n          growMemory(input.byteLength, size);\n          memory.set(input, size);\n          length = input.byteLength;\n        }\n        update(0, size, length);\n        state.set(memory.subarray(0, size));\n        return this;\n      },\n      digest() {\n        memory.set(state);\n        return finalize(digest(0));\n      },\n    };\n  }\n\n  // Logical shift right makes it an u32, otherwise it's interpreted as an i32.\n  function forceUnsigned32(i) {\n    return i >>> 0;\n  }\n\n  // BigInts are arbitrary precision and signed, so to get the \"correct\" u64\n  // value from the return, we'll need to force that interpretation.\n  const u64Max = 2n ** 64n - 1n;\n  function forceUnsigned64(i) {\n    return i & u64Max;\n  }\n\n  const encoder = new TextEncoder();\n  const defaultSeed = 0;\n  const defaultBigSeed = 0n;\n\n  function h32(str, seed = defaultSeed) {\n    // https://developer.mozilla.org/en-US/docs/Web/API/TextEncoder/encodeInto#buffer_sizing\n    // By sizing the buffer to 3 * string-length we guarantee that the buffer\n    // will be appropriately sized for the utf-8 encoding of the string.\n    growMemory(str.length * 3, 0);\n    return forceUnsigned32(\n      xxh32(0, encoder.encodeInto(str, memory).written, seed),\n    );\n  }\n\n  function h64(str, seed = defaultBigSeed) {\n    growMemory(str.length * 3, 0);\n    return forceUnsigned64(\n      xxh64(0, encoder.encodeInto(str, memory).written, seed),\n    );\n  }\n\n  return {\n    h32,\n    h32ToString(str, seed = defaultSeed) {\n      return h32(str, seed).toString(16).padStart(8, \"0\");\n    },\n    h32Raw(inputBuffer, seed = defaultSeed) {\n      growMemory(inputBuffer.byteLength, 0);\n      memory.set(inputBuffer);\n      return forceUnsigned32(xxh32(0, inputBuffer.byteLength, seed));\n    },\n    create32(seed = defaultSeed) {\n      return create(\n        XXH32_STATE_SIZE_BYTES,\n        seed,\n        init32,\n        update32,\n        digest32,\n        forceUnsigned32,\n      );\n    },\n    h64,\n    h64ToString(str, seed = defaultBigSeed) {\n      return h64(str, seed).toString(16).padStart(16, \"0\");\n    },\n    h64Raw(inputBuffer, seed = defaultBigSeed) {\n      growMemory(inputBuffer.byteLength, 0);\n      memory.set(inputBuffer);\n      return forceUnsigned64(xxh64(0, inputBuffer.byteLength, seed));\n    },\n    create64(seed = defaultBigSeed) {\n      return create(\n        XXH64_STATE_SIZE_BYTES,\n        seed,\n        init64,\n        update64,\n        digest64,\n        forceUnsigned64,\n      );\n    },\n  };\n}\n"], "names": ["wasmBytes", "Uint8Array", "xxhash", "instance", "exports", "mem", "xxh32", "xxh64", "init32", "update32", "digest32", "init64", "update64", "digest64", "memory", "buffer", "growMemory", "length", "offset", "byteLength", "extraPages", "Math", "ceil", "grow", "create", "size", "seed", "init", "update", "digest", "finalize", "state", "set", "subarray", "input", "encoder", "encodeInto", "written", "forceUnsigned32", "i", "u64Max", "forceUnsigned64", "TextEncoder", "defaultSeed", "defaultBigSeed", "h32", "str", "h64", "h32ToString", "toString", "padStart", "h32Raw", "inputBuffer", "create32", "u32_BYTES", "h64ToString", "h64Raw", "create64", "u64_BYTES", "WebAssembly", "instantiate"], "mappings": "yCAOA,MAAMA,EAAY,IAAIC,WAAW,CAAA,EAAA,GAAA,IAAA,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,IAAA,IAAA,IAAA,EAAA,GAAA,EAAA,IAAA,IAAA,EAAA,GAAA,EAAA,IAAA,EAAA,IAAA,GAAA,EAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,IAAA,IAAA,EAAA,GAAA,EAAA,IAAA,EAAA,IAAA,EAAA,GAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA,EAAA,EAAA,IAAA,IAAA,IAAA,EAAA,EAAA,EAAA,IAAA,IAAA,IAAA,GAAA,GAAA,EAAA,EAAA,EAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,EAAA,EAAA,EAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,EAAA,EAAA,EAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,EAAA,EAAA,EAAA,IAAA,IAAA,IAAA,GAAA,GAAA,EAAA,EAAA,EAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,EAAA,EAAA,EAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,EAAA,EAAA,EAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,EAAA,EAAA,GAAA,IAAA,GAAA,GAAA,IAAA,EAAA,EAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,IAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,IAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,IAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,IAAA,EAAA,EAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,GAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,GAAA,GAAA,IAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,GAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,GAAA,EAAA,IAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,GAAA,IAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,IAAA,IAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,GAAA,EAAA,GAAA,EAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,EAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,IAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,IAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,IAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,IAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,GAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,GAAA,EAAA,IAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,GAAA,IAAA,EAAA,EAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,EAAA,GAAA,EAAA,GAAA,GAAA,IAAA,IAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,IAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,GAAA,EAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,GAAA,EAAA,GAAA,GAAA,IAAA,IAAA,GAAA,EAAA,GAAsB,SAExC,iBACb,OAAOC,ACWF,SAAgBC,CAAQ,EAC7B,KAAM,CACJC,QAAS,CACPC,IAAAA,CAAG,CACHC,MAAAA,CAAK,CACLC,MAAAA,CAAK,CACLC,OAAAA,CAAM,CACNC,SAAAA,CAAQ,CACRC,SAAAA,CAAQ,CACRC,OAAAA,CAAM,CACNC,SAAAA,CAAQ,CACRC,SAAAA,CAAQ,CACT,CACF,CAAGV,EAEJ,IAAIW,EAAS,IAAIb,WAAWI,EAAIU,MAAM,EAEtC,SAASC,EAAWC,CAAM,CAAEC,CAAM,EAChC,GAAIb,EAAIU,MAAM,CAACI,UAAU,CAAGF,EAASC,EAAQ,CAC3C,MAAME,EAAaC,KAAKC,IAAI,CAE1B,AAACL,CAAAA,EAASC,EAASb,EAAIU,MAAM,CAACI,UAAU,AAAA,EAAK,MAE/Cd,CAAAA,EAAIkB,IAAI,CAACH,GAITN,EAAS,IAAIb,WAAWI,EAAIU,MAAM,CACpC,CACF,CAIA,SAASS,EAAOC,CAAI,CAAEC,CAAI,CAAEC,CAAI,CAAEC,CAAM,CAAEC,CAAM,CAAEC,CAAQ,EAGxDd,EAAWS,GAGX,MAAMM,EAAQ,IAAI9B,WAAWwB,GAQ7B,OAPAX,EAAOkB,GAAG,CAACD,GACXJ,EAAK,EAAGD,GAIRK,EAAMC,GAAG,CAAClB,EAAOmB,QAAQ,CAAC,EAAGR,IAEtB,CACLG,OAAOM,CAAK,EAENjB,IAAAA,EAYJ,OAbAH,EAAOkB,GAAG,CAACD,GAEU,UAAjB,OAAOG,EACTlB,CAAAA,EAA0B,EAAfkB,EAAMjB,MAAM,CAAMQ,GAC7BR,EAASkB,EAAQC,UAAU,CAACF,EAAOpB,EAAOmB,QAAQ,CAACR,IAAOY,OAAO,AAAA,EAGjErB,CAAAA,EAAWkB,EAAMf,UAAU,CAAEM,GAC7BX,EAAOkB,GAAG,CAACE,EAAOT,GAClBR,EAASiB,EAAMf,UAAU,AAAVA,EAEjBS,EAAO,EAAGH,EAAMR,GAChBc,EAAMC,GAAG,CAAClB,EAAOmB,QAAQ,CAAC,EAAGR,IACtB,IAAI,AACb,EACAI,OAAAA,IACEf,CAAAA,EAAOkB,GAAG,CAACD,GACJD,EAASD,EAAO,GAAA,CAE3B,CACF,CAGA,SAASS,EAAgBC,CAAC,EACxB,OAAOA,IAAM,CACf,CAIA,MAAMC,EAAS,CAAE,AAAF,CAAE,EAAI,EAAG,AAAH,CAAG,CAAG,CAAE,AAAF,CAAE,CAC7B,SAASC,EAAgBF,CAAC,EACxB,OAAOA,EAAIC,CACb,CAEA,MAAML,EAAU,IAAIO,YACdC,EAAc,EACdC,EAAiB,CAAE,AAAF,CAAE,CAEzB,SAASC,EAAIC,CAAG,CAAEpB,EAAOiB,CAAW,EAKlC,OADA3B,EAAW8B,EAAAA,EAAI7B,MAAM,CAAM,GACpBqB,EACLhC,EAAM,EAAG6B,EAAQC,UAAU,CAACU,EAAKhC,GAAQuB,OAAO,CAAEX,GAEtD,CAEA,SAASqB,EAAID,CAAG,CAAEpB,EAAOkB,CAAc,EAErC,OADA5B,EAAW8B,EAAAA,EAAI7B,MAAM,CAAM,GACpBwB,EACLlC,EAAM,EAAG4B,EAAQC,UAAU,CAACU,EAAKhC,GAAQuB,OAAO,CAAEX,GAEtD,CAEA,MAAO,CACLmB,IAAAA,EACAG,YAAAA,CAAYF,EAAKpB,EAAOiB,CAAW,GAC1BE,EAAIC,EAAKpB,GAAMuB,QAAQ,CAAC,IAAIC,QAAQ,CAAC,EAAG,KAEjDC,OAAAA,CAAOC,EAAa1B,EAAOiB,CAAW,GACpC3B,CAAAA,EAAWoC,EAAYjC,UAAU,CAAE,GACnCL,EAAOkB,GAAG,CAACoB,GACJd,EAAgBhC,EAAM,EAAG8C,EAAYjC,UAAU,CAAEO,GAAAA,EAE1D2B,SAAAA,CAAS3B,EAAOiB,CAAW,GAClBnB,EAnIX8B,GAqIM5B,EACAlB,EACAC,EACAC,EACA4B,GAGJS,IAAAA,EACAQ,YAAAA,CAAYT,EAAKpB,EAAOkB,CAAc,GAC7BG,EAAID,EAAKpB,GAAMuB,QAAQ,CAAC,IAAIC,QAAQ,CAAC,GAAI,KAElDM,OAAAA,CAAOJ,EAAa1B,EAAOkB,CAAc,GACvC5B,CAAAA,EAAWoC,EAAYjC,UAAU,CAAE,GACnCL,EAAOkB,GAAG,CAACoB,GACJX,EAAgBlC,EAAM,EAAG6C,EAAYjC,UAAU,CAAEO,GAAAA,EAE1D+B,SAAAA,CAAS/B,EAAOkB,CAAc,GACrBpB,EA7IXkC,GA+IMhC,EACAf,EACAC,EACAC,EACA4B,EAGN,CACF,ED3JgB,AAAC,CAAA,MAAMkB,YAAYC,WAAW,CAAC5D,EAAS,EAAGG,QAAQ,CACnE"}