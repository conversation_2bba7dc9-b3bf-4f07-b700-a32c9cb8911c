"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;
const XCLAIM_1 = require("./XCLAIM");
var XCLAIM_2 = require("./XCLAIM");
Object.defineProperty(exports, "FIRST_KEY_INDEX", { enumerable: true, get: function () { return XCLAIM_2.FIRST_KEY_INDEX; } });
function transformArguments(...args) {
    return [
        ...(0, XCLAIM_1.transformArguments)(...args),
        'JUSTID'
    ];
}
exports.transformArguments = transformArguments;
