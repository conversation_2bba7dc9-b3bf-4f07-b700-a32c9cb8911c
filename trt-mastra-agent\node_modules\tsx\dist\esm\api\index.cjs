"use strict";var m=Object.defineProperty;var a=(r,e)=>m(r,"name",{value:e,configurable:!0});var q=require("../../register-2sWVXuRQ.cjs");require("../../get-pipe-path-BoR10qr8.cjs");var t=require("../../register-D46fvsV_.cjs");require("../../require-D4F1Lv60.cjs");var n=require("../../node-features-roYmp9jK.cjs");require("node:module"),require("node:worker_threads"),require("node:url"),require("module"),require("node:path"),require("../../temporary-directory-B83uKxJF.cjs"),require("node:os"),require("get-tsconfig"),require("node:fs"),require("../../index-gckBtVBf.cjs"),require("esbuild"),require("node:crypto"),require("../../client-D6NvIMSC.cjs"),require("node:net"),require("node:util"),require("../../index-BWFBUo6r.cjs");const c=a((r,e)=>{if(!e||typeof e=="object"&&!e.parentURL)throw new Error("The current file path (import.meta.url) must be provided in the second argument of tsImport()");const i=typeof e=="string",u=i?e:e.parentURL,s=Date.now().toString(),o=t.register({namespace:s});return!n.isFeatureSupported(n.esmLoadReadFile)&&!t.isBarePackageNamePattern.test(r)&&t.cjsExtensionPattern.test(r)?Promise.resolve(o.require(r,u)):q.register({namespace:s,...i?{}:e}).import(r,u)},"tsImport");exports.register=q.register,exports.tsImport=c;
