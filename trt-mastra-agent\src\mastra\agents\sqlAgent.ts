import { AgentClient } from "mastra";
import { sqlExecutor } from "../tools/sqlExecutor";
import { google } from "@ai-sdk/google";

const client = new google.generativeLanguage.v1beta2.LanguageServiceClient();

export const sqlAgent: AgentClient = {
  name: "sqlAgent",
  description: "Doğal dili SQL'e çevirir ve MCP API'ye sorgu gönderir.",
  tools: [sqlExecutor],
  async run(input: { query: string }) {
    const prompt = `
      Aşağıdaki cümleyi SQL sorgusuna çevir:
      "${input.query}"
      Sadece SQL sorgusunu ver, başka bir şey yazma.
    `;

    const response = await client.generateText({
      model: "models/text-bison-001",
      prompt: {
        text: prompt,
      },
      temperature: 0,
    });

    const sqlQuery = response[0]?.candidates[0]?.output.trim() ?? "";

    if (!sqlQuery) {
      throw new Error("SQL sorgusu oluşturulamadı.");
    }

    const result = await sqlExecutor.run({ sql: sqlQuery });
    return result;
  },
};
