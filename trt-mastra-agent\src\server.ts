import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { sqlWorkflow } from './mastra/workflows/sqlWorkflow';

// Environment variables'ları yükle
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        service: 'Mastra SQL Agent'
    });
});

// Ana chatbot endpoint
app.post('/chat', async (req, res) => {
    try {
        const { query } = req.body;

        if (!query) {
            return res.status(400).json({
                error: 'Sorgu gereklidir'
            });
        }

        console.log(`📝 Kullanıcı sorgusu: ${query}`);

        // Workflow'u çalıştır
        const result = await sqlWorkflow.execute({ query });

        console.log(`✅ Workflow sonucu:`, JSON.stringify(result, null, 2));

        res.json({
            success: true,
            query: query,
            result: result.result // Nested result'u düzelt
        });

    } catch (error) {
        console.error('❌ Chat endpoint hatası:', error);
        console.error('Hata detayı:', error instanceof Error ? error.stack : error);
        res.status(500).json({
            error: error instanceof Error ? error.message : 'Bilinmeyen hata'
        });
    }
});

// Test endpoint
app.post('/test', async (req, res) => {
    try {
        const testQuery = "Maaşı 5000'den büyük personelleri getir";
        
        console.log(`🧪 Test sorgusu: ${testQuery}`);
        
        const result = await sqlWorkflow.execute({ query: testQuery });
        
        res.json({
            success: true,
            testQuery,
            result
        });
        
    } catch (error) {
        console.error('❌ Test endpoint hatası:', error);
        res.status(500).json({ 
            error: error instanceof Error ? error.message : 'Test başarısız'
        });
    }
});

// Sunucuyu başlat
app.listen(PORT, () => {
    console.log(`🚀 Mastra SQL Agent sunucusu http://localhost:${PORT} adresinde çalışıyor`);
    console.log(`📋 Endpoints:`);
    console.log(`   GET  /health - Sağlık kontrolü`);
    console.log(`   POST /chat   - Chatbot sorguları`);
    console.log(`   POST /test   - Test sorgusu`);
    console.log(`\n💡 Kullanım örneği:`);
    console.log(`   curl -X POST http://localhost:${PORT}/chat -H "Content-Type: application/json" -d '{"query":"Yaşı 30 dan küçük erkekleri getir"}'`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 Sunucu kapatılıyor...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🛑 Sunucu kapatılıyor...');
    process.exit(0);
});
