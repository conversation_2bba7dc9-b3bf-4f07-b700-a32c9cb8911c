{"name": "@upstash/redis", "version": "v1.35.1", "main": "./nodejs.js", "module": "./nodejs.mjs", "types": "./nodejs.d.ts", "exports": {".": {"import": "./nodejs.mjs", "require": "./nodejs.js"}, "./node": {"import": "./nodejs.mjs", "require": "./nodejs.js"}, "./cloudflare": {"import": "./cloudflare.mjs", "require": "./cloudflare.js"}, "./cloudflare.js": {"import": "./cloudflare.mjs", "require": "./cloudflare.js"}, "./cloudflare.mjs": {"import": "./cloudflare.mjs", "require": "./cloudflare.js"}, "./fastly": {"import": "./fastly.mjs", "require": "./fastly.js"}, "./fastly.js": {"import": "./fastly.mjs", "require": "./fastly.js"}, "./fastly.mjs": {"import": "./fastly.mjs", "require": "./fastly.js"}}, "description": "An HTTP/REST based Redis client built on top of Upstash REST API.", "repository": {"type": "git", "url": "git+https://github.com/upstash/upstash-redis.git"}, "keywords": ["redis", "database", "serverless", "edge", "upstash"], "files": ["./*"], "scripts": {"build": "tsup && cp package.json README.md LICENSE dist/", "test": "bun test pkg", "fmt": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint \"**/*.{js,ts,tsx}\" --quiet --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "lint:fix": "eslint . -c .ts,.tsx,.js,.jsx --fix", "commit": "cz", "lint:format": "bun run lint:fix && bun run format", "check-exports": "bun run build && cd dist && attw -P"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/upstash/upstash-redis/issues"}, "homepage": "https://github.com/upstash/upstash-redis#readme", "devDependencies": {"@biomejs/biome": "latest", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "8.4.0", "@typescript-eslint/parser": "8.4.0", "bun-types": "1.0.33", "eslint": "9.10.0", "eslint-plugin-unicorn": "55.0.0", "husky": "^9.1.1", "prettier": "^3.3.3", "tsup": "^8.2.3", "typescript": "latest"}, "dependencies": {"uncrypto": "^0.1.3"}}