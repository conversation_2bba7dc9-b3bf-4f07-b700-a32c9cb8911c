{"version": 3, "file": "server-config.mjs", "sources": ["../../src/mastra/index.ts"], "sourcesContent": ["import { Mastra } from '@mastra/core';\nimport { sqlAgent } from './agents/sqlAgent';\nimport { sqlWorkflow } from \"./workflows/sqlWorkflow\";\nimport dotenv from \"dotenv\";\n\n// Environment variables'ları yükle\ndotenv.config();\n\n// Mastra instance'ını oluştur ve export et\nexport const mastra = new Mastra({\n  agents: {\n    sqlAgent: sqlAgent\n  }\n});\n\nasync function main() {\n  console.log(\"🚀 SQL Agent başlatılıyor...\");\n\n  // Test sorgusu\n  const testQuery = \"Maaşı 5000'den büyük ve yaşı 30'dan küçük erkekleri getir\";\n\n  try {\n    const result = await sqlWorkflow.execute({\n      query: testQuery,\n    });\n\n    console.log(\"✅ Sorgu sonucu:\", JSON.stringify(result, null, 2));\n  } catch (error) {\n    console.error(\"❌ Hata:\", error);\n  }\n}\n\n// Eğer bu dosya doğrudan çalıştırılıyorsa main fonksiyon<PERSON>u çalıştır\nif (import.meta.url === `file://${process.argv[1]}`) {\n  main().catch(console.error);\n}\n"], "names": [], "mappings": "AAEA,MAAS,SAAA;;;;"}